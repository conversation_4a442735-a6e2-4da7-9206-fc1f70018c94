import jwt from "jsonwebtoken";
import axios from "axios";

const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res
      .status(401)
      .json({ message: "Unauthorized: Token missing or expired" });
  }
  const access_token = authHeader.split(" ")[1];

  try {
    // First, try to verify with Supabase Auth server
    const supabaseUrl = process.env.SUPABASE_URL;
    if (supabaseUrl) {
      try {
        const response = await axios.get(`${supabaseUrl}/auth/v1/user`, {
          headers: {
            Authorization: `Bearer ${access_token}`,
            apikey: process.env.SUPABASE_ANON_KEY,
          },
        });

        if (response.status === 200) {
          req.user = response.data;
          console.log("User authenticated with Supabase:", response.data);
          return next();
        }
      } catch (supabaseErr) {
        console.log(
          "Supabase auth failed, trying custom JWT...",
          supabaseErr.response?.status
        );
      }
    }

    // Fallback to custom JWT verification
    if (!process.env.JWT_SECRET) {
      return res
        .status(500)
        .json({ message: "Internal Server Error: JWT_SECRET is not defined" });
    }

    jwt.verify(access_token, process.env.JWT_SECRET, (err, user) => {
      if (err) {
        return res
          .status(403)
          .json({ message: "Forbidden: Invalid or expired token" });
      }
      req.user = user;
      console.log("User authenticated with custom JWT:", user);
      next();
    });
  } catch (error) {
    console.error("Authentication error:", error);
    return res
      .status(403)
      .json({ message: "Forbidden: Invalid or expired token" });
  }
};

export default authenticateToken;
