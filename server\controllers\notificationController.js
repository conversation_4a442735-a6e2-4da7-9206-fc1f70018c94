import { Notification } from "../models/index.js";

// Get all notifications for a user
export const getUserNotifications = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const notifications = await Notification.findAll({
      where: { user_id: userId },
      order: [['created_at', 'DESC']]
    });

    res.status(200).json({
      success: true,
      data: notifications
    });
  } catch (error) {
    console.error("Error fetching notifications:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch notifications",
      error: error.message
    });
  }
};

// Create a new notification
export const createNotification = async (req, res) => {
  try {
    const { user_id, title, message, icon_url } = req.body;

    const notification = await Notification.create({
      user_id,
      title,
      message,
      icon_url,
      read: false
    });

    res.status(201).json({
      success: true,
      data: notification
    });
  } catch (error) {
    console.error("Error creating notification:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create notification",
      error: error.message
    });
  }
};

// Mark notification as read
export const markNotificationAsRead = async (req, res) => {
  try {
    const { notificationId } = req.params;

    const [updatedRowsCount] = await Notification.update(
      { read: true },
      { where: { id: notificationId } }
    );

    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: "Notification not found"
      });
    }

    const updatedNotification = await Notification.findByPk(notificationId);

    res.status(200).json({
      success: true,
      data: updatedNotification
    });
  } catch (error) {
    console.error("Error marking notification as read:", error);
    res.status(500).json({
      success: false,
      message: "Failed to mark notification as read",
      error: error.message
    });
  }
};

// Get unread notification count
export const getUnreadCount = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const unreadCount = await Notification.count({
      where: { 
        user_id: userId,
        read: false 
      }
    });

    res.status(200).json({
      success: true,
      data: { unreadCount }
    });
  } catch (error) {
    console.error("Error getting unread count:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get unread count",
      error: error.message
    });
  }
};
