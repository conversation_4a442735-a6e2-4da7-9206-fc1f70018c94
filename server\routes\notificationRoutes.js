import express from "express";
import {
  getUserNotifications,
  createNotification,
  markNotificationAsRead,
  getUnreadCount,
} from "../controllers/notificationController.js";
import authenticateToken from "../authenticateToken.js";

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// GET /api/notifications/:userId - Get all notifications for a user
router.get("/:userId", getUserNotifications);

// POST /api/notifications - Create a new notification
router.post("/", createNotification);

// PUT /api/notifications/:notificationId/read - Mark notification as read
router.put("/:notificationId/read", markNotificationAsRead);

// GET /api/notifications/:userId/unread-count - Get unread notification count
router.get("/:userId/unread-count", getUnreadCount);

export default router;
