# Authentication Setup Guide

## Masalah yang Dipecahkan

Aplikasi menggunakan **dual authentication system**:
- **Frontend**: Menggunakan Supabase Auth untuk login/signup
- **Backend**: Memerlukan token verification untuk API endpoints

### Masalah Sebelumnya
- Backend mengharapkan JWT token yang di-sign dengan `JWT_SECRET`
- Frontend mengirim Supabase access token
- Mismatch ini menyebabkan error 401 "Unauthorized: Token missing or expired"

## Solusi yang Diterapkan

### 1. Updated Authentication Middleware (`authenticateToken.js`)

Middleware sekarang mendukung **dual token verification**:

1. **Primary**: Verifikasi dengan Supabase Auth server
2. **Fallback**: Verifikasi dengan custom JWT secret

```javascript
// Verifikasi dengan Supabase Auth server
const response = await axios.get(`${supabaseUrl}/auth/v1/user`, {
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'apikey': process.env.SUPABASE_ANON_KEY
  }
});
```

### 2. Environment Variables yang Diperlukan

Tambahkan ke file `.env` di folder `server/`:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# JWT Configuration (untuk fallback)
JWT_SECRET=your_custom_jwt_secret_here
```

### 3. Cara Mendapatkan Supabase Credentials

1. **SUPABASE_URL**: 
   - Buka Supabase Dashboard
   - Pilih project Anda
   - Go to Settings > API
   - Copy "Project URL"

2. **SUPABASE_ANON_KEY**:
   - Di halaman yang sama (Settings > API)
   - Copy "anon public" key

## Alur Authentication Setelah Fix

### Saat Signup:
1. ✅ Frontend → Supabase Auth (signup berhasil)
2. ✅ Frontend → Backend API (dengan Supabase token)
3. ✅ Backend → Verifikasi token dengan Supabase Auth server
4. ✅ Backend → Proses request (create user data)

### Saat Login:
1. ✅ Frontend → Supabase Auth (login berhasil)
2. ✅ Frontend → Backend API (dengan Supabase token)
3. ✅ Backend → Verifikasi token dengan Supabase Auth server
4. ✅ Backend → Proses request

## Testing

Untuk memastikan fix bekerja:

1. **Test Signup**:
   ```bash
   # Jalankan frontend dan backend
   # Coba signup dengan user baru
   # Check network tab - PUT request ke /api/users/{id} harus berhasil (200/201)
   ```

2. **Test Login**:
   ```bash
   # Login dengan user yang sudah ada
   # Check semua API calls berhasil
   ```

## Troubleshooting

### Error: "SUPABASE_URL is not defined"
- Pastikan file `.env` ada di folder `server/`
- Pastikan `SUPABASE_URL` dan `SUPABASE_ANON_KEY` sudah diset

### Error: "Network Error" saat verifikasi
- Check apakah Supabase URL benar
- Check apakah SUPABASE_ANON_KEY valid

### Masih error 401
- Check console log di backend untuk detail error
- Pastikan token yang dikirim frontend valid
- Test dengan Postman menggunakan token dari browser

## Keuntungan Solusi Ini

1. **Backward Compatible**: Masih support custom JWT jika diperlukan
2. **Secure**: Verifikasi langsung dengan Supabase Auth server
3. **Flexible**: Bisa switch antara Supabase dan custom auth
4. **Real-time**: Token validation selalu up-to-date dengan Supabase

## Next Steps

Setelah fix ini diterapkan, Anda bisa:
1. Remove custom JWT logic jika tidak diperlukan
2. Implement refresh token handling
3. Add role-based access control (RBAC)
