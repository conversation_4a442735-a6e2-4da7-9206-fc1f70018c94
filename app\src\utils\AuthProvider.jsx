import PropTypes from "prop-types";
import { useState, useEffect, createContext, useContext } from "react";
import {
  getUserProgress,
  updateUserProgress,
  updateUser,
} from "../controllers/UserController";
import { supabase } from "./supabaseClient";
import { parseISO } from "date-fns";

// For sharing the user state across the app
const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Cleanup function for localStorage
  const cleanupPendingData = () => {
    localStorage.removeItem("pendingUserData");
    console.log("🧹 Cleaned up pending user data");
  };

  // Retrieve the session information
  useEffect(() => {
    const fetchSession = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          throw error;
        }

        const session = data?.session;
        if (session) {
          setUser(session.user);
        } else {
          setUser(null);
          // Clean up pending data if no session
          cleanupPendingData();
        }
      } catch (error) {
        setUser(null);
        cleanupPendingData();
      } finally {
        setLoading(false);
      }
    };

    // Fetch the user from supabase
    fetchSession();

    // Listener for changes on auth state
    const { data } = supabase.auth.onAuthStateChange(async (event, session) => {
      // JWT token provided by supabase
      const access_token = session?.access_token;

      if (event === "INITIAL_SESSION") {
      } else if (event === "SIGNED_IN" && access_token) {
        setUser(session?.user ?? null);
        setLoading(false);

        // Check for pending user data from signup
        const pendingUserData = localStorage.getItem("pendingUserData");
        if (pendingUserData) {
          try {
            const userData = JSON.parse(pendingUserData);
            console.log(
              "🔄 Processing pending user data after email confirmation:",
              userData
            );

            // Verify the pending user ID matches current user
            if (userData.id === session.user.id) {
              // Create user data in backend
              await updateUser(userData.id, userData);
              console.log("✅ Pending user data saved successfully");
            } else {
              console.log(
                "⚠️ Pending user ID doesn't match current user, skipping"
              );
            }

            // Clear pending data regardless
            localStorage.removeItem("pendingUserData");
          } catch (error) {
            console.error("❌ Error processing pending user data:", error);
            // Clear corrupted data
            localStorage.removeItem("pendingUserData");
          }
        }
      } else if (event === "SIGNED_OUT") {
        setUser(null);
        // Clear any pending data on signout
        localStorage.removeItem("pendingUserData");
      } else if (event === "TOKEN_REFRESHED") {
        console.log("Access token refreshed");
      }
    });

    // Cleanup
    return () => {
      if (data) data.subscription.unsubscribe();
    };
  }, []);

  // Note:
  // This function doesn' work because last_sign_in_at is already update when the user is signed in
  useEffect(() => {
    if (user) {
      const checkDailyLoginPoints = async () => {
        const lastSignInAt = user.last_sign_in_at;
        if (lastSignInAt && isDifferentDate(lastSignInAt)) {
          let progress = (await getUserProgress(user.id)) ?? {
            level_progress: 0,
          };
          const updatedProgress = {
            ...progress,
            level_progress: progress.level_progress + 5,
          };
          await updateUserProgress(user.id, updatedProgress);
        }
      };
      checkDailyLoginPoints();
    }
  }, [user]);

  const isDifferentDate = (lastSignInAt) => {
    const lastSignInUTC = parseISO(lastSignInAt);
    const now = new Date();

    const yesterday = new Date(
      Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() - 1)
    );
    return lastSignInUTC <= yesterday;
  };

  // Sign out the user
  const handleSignOut = async () => {
    // Sign out from supabase autehtication
    try {
      // Check if there is an active session
      const { data: session, error: sessionError } =
        await supabase.auth.getSession();
      if (sessionError || !session) {
        console.log("No active session found. Clearing user state.");
        setUser(null);
        window.localStorage.clear();
        return;
      }

      // Sign out the user
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw error;
      }
      setUser(null);
    } catch (error) {
      console.error("User failed to sign out", error);
      setUser(null);
      window.localStorage.clear();
      return;
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, handleSignOut }}>
      {children}
    </AuthContext.Provider>
  );
}

AuthProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export const useAuth = () => useContext(AuthContext);
