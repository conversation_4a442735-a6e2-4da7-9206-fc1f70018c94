import React, { useState, useEffect } from "react";
import axiosClient from "../utils/axiosClient";
import NotificationsIcon from "@mui/icons-material/Notifications";
import IconButton from "@mui/material/IconButton";
import Drawer from "@mui/material/Drawer";
import Box from "@mui/material/Box";
import { useAuth } from "../utils/AuthProvider.jsx";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import ListItemAvatar from "@mui/material/ListItemAvatar";
import badgePlaceholder from "/assets/badge_placeholder.png";
import "./Notifications.css";

import { Button, Divider, Grid2 as Grid, Typography } from "@mui/material";
import Toolbar from "@mui/material/Toolbar";
import CloseIcon from "@mui/icons-material/Close";
import filledNotificationIcon from "/assets/NotificationsFilled.png"; // Adjust path based on folder structure

// Notifications Icon in Header
function Notifications() {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadNotifications, setUnreadNotifications] = useState(0);

  const toggleDrawer = () => {
    setOpen(!open);
  };
  useEffect(() => {
    // Fetch initial data
    const fetchNotifications = async () => {
      try {
        const response = await axiosClient.get(`/notifications/${user.id}`);
        if (response.data.success) {
          const initialNotifications = response.data.data;
          setNotifications(initialNotifications);
          console.log("Notifications fetched:", initialNotifications);
          const unreadNotifications = initialNotifications.filter(
            (notification) => !notification.read
          );
          setUnreadNotifications(unreadNotifications.length);
        }
      } catch (error) {
        console.error("Error fetching notifications:", error);
      }
    };

    if (user?.id) {
      fetchNotifications();
    }

    // Note: Real-time subscription removed for now
    // Can be implemented later with WebSocket or polling
  }, []);
  const markAllAsRead = async () => {
    for (const notification of notifications) {
      if (!notification.read) await markAsRead(notification.id);
    }
    setUnreadNotifications(0);
  };

  const markAsRead = async (notificationId) => {
    try {
      const response = await axiosClient.put(`/notifications/${notificationId}/read`);
      if (response.data.success) {
        const updatedNotification = response.data.data;

        // Update notifications state
        setNotifications((prevNotifications) =>
          prevNotifications.map((notification) =>
            notification.id === updatedNotification.id
              ? updatedNotification
              : notification
          )
        );
        if (updatedNotification.read) {
          setUnreadNotifications(unreadNotifications - 1);
        }
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  return (
    <>
      <Box role="presentation" onClick={() => toggleDrawer()}>
        <div className="notification-wrapper">
          {unreadNotifications > 0 ? (
            <div className="notification-count">
              <Typography variant="body3">{unreadNotifications}</Typography>
            </div>
          ) : (
            ""
          )}
          <IconButton type="button" aria-label="Notifications">
            <NotificationsIcon
              sx={{
                color: "#353E45",
                "&:hover": {
                  color: "#2D90E0", // changes text color on hover
                },
              }}
            />
          </IconButton>
        </div>
      </Box>
      <Drawer
        anchor="right"
        open={open}
        BackdropProps={{ invisible: true }}
        onClose={() => toggleDrawer()}
      >
        <Toolbar />
        <Box role="presentation">
          <Box
            container
            display="flex"
            flexDirection="row"
            justifyContent="space-between"
            padding=".5rem"
            spacing={2}
          >
            <Typography variant="body" sx={{ alignContent: "center" }}>
              Notifications
            </Typography>
            <Button onClick={() => toggleDrawer()}>
              <CloseIcon />
            </Button>
          </Box>
          <Divider />
          <Grid container>
            <Button
              onClick={markAllAsRead}
              disabled={notifications.length == 0}
            >
              <Typography variant="body">Mark all as read</Typography>
            </Button>
          </Grid>
          <Divider />
          <List sx={{ width: { xs: "100vw", sm: "380px", md: "45vw" } }}>
            {notifications.length > 0 ? (
              notifications.map((notification, index) => (
                <React.Fragment key={index}>
                  <ListItem
                    key={index}
                    disablePadding
                    alignItems="flex-start"
                    className={notification.read ? "" : "unread-notification"}
                  >
                    <ListItemButton
                      onClick={() => markAsRead(notification.id)}
                      sx={{ display: "flex", gap: "1rem" }}
                    >
                      <ListItemAvatar>
                        <img
                          width="50px"
                          alt="Notification Icon"
                          src={
                            notification.icon_url
                              ? notification.icon_url
                              : badgePlaceholder
                          }
                        />
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box
                            display="flex"
                            flexDirection="column"
                            gap={1}
                            justifyContent="start"
                          >
                            <Typography
                              component="span"
                              variant="h5"
                              sx={{
                                color: "text.primary",
                                display: "block",
                                fontWeight: 700,
                              }}
                            >
                              {notification.title}
                            </Typography>
                            <Typography
                              component="span"
                              variant="body1"
                              sx={{
                                color: "text.primary",
                                display: "block",
                              }}
                            >
                              {notification.message}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <React.Fragment>
                            <Typography
                              component="span"
                              variant="body3"
                              sx={{ color: "text.primary", display: "block" }}
                            >
                              {`${new Date(notification.created_at).toDateString().toLocaleUpperCase()} - ${new Date(notification.created_at).toLocaleTimeString()}`}
                            </Typography>
                          </React.Fragment>
                        }
                      />
                    </ListItemButton>
                  </ListItem>
                  {index < notifications.length - 1 && <Divider />}
                </React.Fragment>
              ))
            ) : (
              <Box container textAlign={"center"}>
                <img src={filledNotificationIcon} />
                <Typography variant="body2">No notifications!</Typography>
              </Box>
            )}
          </List>
        </Box>
      </Drawer>
    </>
  );
}
export default Notifications;
